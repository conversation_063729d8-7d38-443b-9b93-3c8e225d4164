param(
    [string]$commitId,
    [string]$projectPath = $pwd.Path
)

if (-not $commitId) {
    Write-Error "请输入提交记录ID！"
    exit 1
}

if (-not (Test-Path -Path $projectPath -PathType Container)) {
    Write-Error "项目路径不存在：$projectPath"
    exit 1
}

if (-not (Test-Path -Path ".git" -PathType Container) -and -not (Test-Path -Path "$projectPath/.git" -PathType Container)) {
    Write-Error "当前路径或项目路径不是Git仓库！"
    exit 1
}

$files = git diff-tree --no-commit-id --name-only -r $commitId | Where-Object { $_ -notmatch '^D' }

if (-not $files) {
    Write-Host "提交记录 $commitId 中没有可提取的文件！"
    exit 0
}

Write-Host "开始从提交记录 $commitId 创建文件..."
Write-Host "目标路径：$projectPath"
Write-Host "------------------------"

foreach ($file in $files) {
    $fullPath = Join - Path $projectPath - ChildPath $file
    $dir = Split - Path $fullPath - Parent
    if (-not (Test-Path -Path $dir -PathType Container)) {
        New - Item - ItemType Directory - Path $dir - Force | Out - Null
        Write-Host "创建文件夹：$dir"
    }
    if (git show "$commitId:$file" > $fullPath) {
        Write-Host "创建文件：$fullPath"
    } else {
        Write-Host "警告：文件 $file 在提交记录中不存在，已跳过"
    }
}

Write-Host "------------------------"
Write-Host "操作完成！共处理 $($files.Count) 个文件"