pipeline {

  agent   any

    options {
        buildDiscarder(logRotator(numToKeepStr: '2'))
        skipDefaultCheckout true
    }

    environment {
       PROJECT_VERSION = 0.1 // 项目版本, 公共基础库为 public
        DOCKER_REGISTRY = "127.0.0.1:81" //镜像上传选地址
       IMAGE_NAME = "gruul/medusa-admin-open"
       MODEL_NAME = "medusa-admin-open"
           SERVICE_NAME = "${MODEL_NAME}-${PROJECT_VERSION}"
       IMAGE_VERSION = "${BRANCH_NAME}-${PROJECT_VERSION}"
           DEV_NODE = "node-1"
       PORT = "8101"
    }

    stages {
        stage('check out') {

            agent { label 'master'}

            steps {

               checkout scm

               echo "current branch: $BRANCH_NAME"

                           echo "$IMAGE_VERSION"

            }

        }

        stage ('docker build & push') {

            agent { label 'master'}

            steps{

                script {
                        echo 'node-1 branch build & push'

                        docker.withRegistry("http://${DOCKER_REGISTRY}","harbor") {
                            def customImage = docker.build("${IMAGE_NAME}:${IMAGE_VERSION}")
                            customImage.push()
                        }
                }
            }
        }
                //构建到dev环境
        stage ('docker deploy to develop ') {
            // 此处master改dev 推送到dev线上 dev->dev master->test pro -> pro
           agent { label "$DEV_NODE"}

            //           when {
            //     branch 'develop'
            // }

            steps{

      //        echo "NODEIP: $NODEIP"

                echo 'stop old container'
                sh '''CID=$(docker ps -a | grep $MODEL_NAME| awk \'{print $1}\')
                    if [ "$CID" != "" ];then
                        docker rm -f $CID
                    fi'''
                echo 'remove images'

                sh "docker pull $DOCKER_REGISTRY/$IMAGE_NAME:$IMAGE_VERSION"

                echo 'restart'

                sh "docker run -d --name $SERVICE_NAME  --restart always  -p $PORT:80 $DOCKER_REGISTRY/$IMAGE_NAME:$IMAGE_VERSION"
            }
         }
    }
 
}
