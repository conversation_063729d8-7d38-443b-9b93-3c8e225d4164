# 入库单编辑功能实现说明

## 功能概述

为入库单管理模块增加了编辑功能，支持在同一个页面进行新增和编辑操作，并根据入库单状态控制编辑权限。

## 主要功能

### 1. 列表页面编辑功能 (details.vue)

- **编辑按钮显示控制**: 当入库单状态(status)不为100时，显示编辑按钮
- **编辑跳转**: 点击编辑按钮跳转到AddReceipt.vue页面，传递编辑参数
- **操作列优化**: 增加了操作列宽度，同时显示详情和编辑按钮
- **状态显示**: 新增状态列，使用不同颜色的标签显示入库单状态（已提交/草稿/其他状态）

### 2. 新增/编辑页面功能 (AddReceipt.vue)

- **编辑模式检测**: 通过路由参数判断是否为编辑模式
- **数据加载**: 编辑模式下自动加载现有数据到表单
- **API切换**: 根据模式选择不同的提交API（新增用getSubmit，编辑用editProductBuyIn）
- **默认值设置**: 
  - 入库日期默认为当前本地时间
  - 经办人默认为制单人
- **数据计算**: 自动计算总数量和总金额
- **表单验证**: 提交时进行必要的字段验证
- **商品信息显示**: 编辑时正确显示商品编码、规格、单位、总价等信息
- **数据预加载**: 编辑模式下预加载商品库数据，确保下拉框正确显示

### 3. API接口扩展

- **新增编辑API**: `editProductBuyIn` - 用于更新入库单
- **复用现有API**: 使用`queryById`获取入库单详情

## 技术实现

### 类型定义

```typescript
// 编辑模式相关状态
interface EditModeState {
  isEditMode: boolean;
  editId: string;
  currentStatus: number;
}

// 入库单数据接口
interface WareListItem {
  id: string;
  buyNo: string;
  buyDate: string;
  warehouse: string;
  buyTypeName: string;
  preparerName: string;
  buyQuantity: number;
  buyAmount: number;
  remarks: string;
  status: number;
}
```

### 核心方法

1. **checkEditMode()**: 检查是否为编辑模式
2. **loadEditData()**: 加载编辑数据
3. **calculateTotals()**: 计算总数量和总金额
4. **handleSubmit()**: 统一处理新增和编辑提交

### 权限控制

- **状态100**: 已提交状态，不显示编辑按钮
- **状态0**: 草稿状态，可以编辑
- **其他状态**: 根据业务需求控制

## 使用说明

### 新增流程

1. 点击"新增"按钮进入新增页面
2. 系统自动设置：
   - 入库日期为当前时间
   - 经办人为制单人
   - 入库单号自动生成
3. 填写其他必要信息
4. 点击"提交"按钮保存

### 编辑流程

1. 在入库单列表页面，点击"编辑"按钮
2. 系统跳转到编辑页面，自动加载数据：
   - 基本信息：仓库、入库日期、入库单号、入库类型等
   - 商品信息：商品编码、商品名称、规格、单位、数量、单价、总价等
   - 其他信息：经办人、供应商、备注等
3. 修改需要更新的字段
4. 点击"更新"按钮保存修改

### 编辑功能特点

- **数据完整性**: 参考detailed.vue的实现方式，确保所有字段正确加载
- **商品信息显示**: 商品编码、规格、单位、总价等信息完整显示
- **数据预加载**: 自动预加载商品库数据，确保下拉框正常工作
- **重复检查**: 防止选择重复的商品规格
- **自动计算**: 修改数量或单价时自动重新计算总价
- **下拉框显示**: 确保商品编码和单位下拉框在编辑模式下正确显示选中值
- **单位数据**: 确保单位下拉框有正确的数据源和默认选中值

### 默认值设置

- **入库日期**: 新增时默认为当前本地时间，编辑时保持原有时间
- **经办人**: 新增时默认为制单人，编辑时优先使用原有经办人
- **入库单号**: 系统自动生成
- **制单人**: 系统自动设置为当前登录用户

### 技术实现

- **时间格式化**: 使用本地时间而不是UTC时间，确保显示正确的当前时间
- **时区处理**: 避免时区转换问题，直接使用本地时间戳
- **下拉框显示**: 确保编辑模式下商品编码和单位下拉框正确显示选中值
- **数据匹配**: 商品编码使用skuId作为value，单位使用secUnitId作为value，与下拉框选项匹配
- **单位数据源**: 确保productSecUnits数据正确加载，为单位下拉框提供数据源
- **默认单位设置**: 当没有选中单位时，自动设置第一个可用单位作为默认值

### 状态显示

- **已提交**: 绿色标签显示，状态值为100，不可编辑
- **草稿**: 橙色标签显示，状态值为0，可以编辑
- **其他状态**: 灰色标签显示，显示具体状态值

### 权限控制

- 当入库单状态为100时，编辑按钮不显示
- 只有状态不为100的入库单才能进行编辑操作

## 文件修改清单

1. **src/api/warehouse/warehouse.ts**
   - 添加 `editProductBuyIn` API接口

2. **src/views/warehouse/Inventory/details.vue**
   - 增加编辑按钮和编辑方法
   - 优化操作列布局

3. **src/views/warehouse/Inventory/AddReceipt.vue**
   - 增加编辑模式支持
   - 实现数据预填充
   - 优化表单验证和提交逻辑

## 注意事项

1. 编辑功能需要后端提供相应的API支持
2. 状态控制逻辑需要与后端业务规则保持一致
3. 数据验证规则在新增和编辑模式下保持一致
4. 建议在测试环境中充分验证编辑功能的稳定性

## 后续优化建议

1. 添加编辑历史记录功能
2. 实现字段级别的权限控制
3. 增加批量编辑功能
4. 优化用户体验，如添加加载状态提示 