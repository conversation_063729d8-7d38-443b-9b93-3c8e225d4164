<!--
 * @description: 抽离开源版本
 * @Author: chuy<PERSON><PERSON>
 * @Date: 2021-08-20 17:24:11
 * @LastEditors: chuyinlong
 * @LastEditTime: 2021-09-03 09:53:26
-->

# group-mall-admin

# 后台管理页面

## Project setup

```
npm install
```

### 配置环境变量

```
修改 .env.development文件内各类配置
```

### Compiles and hot-reloads for development

```
node版本 12
开发环境请求后台路径不对，修改request.ts文件的baseURL取值
npm run serve
```

### Compiles and minifies for development

```
npm run build
```

### Lints and fixes files

```
npm run lint
```

### 注意打包到测试环境也是用开发的配置文件进行打包，npm 双击此命令 build:develop

```
npm run build:develop
```

## doc

(vue-property-decorator)[https://github.com/kaorun343/vue-property-decorator]

(vuex-class)[https://github.com/ktsn/vuex-class]
