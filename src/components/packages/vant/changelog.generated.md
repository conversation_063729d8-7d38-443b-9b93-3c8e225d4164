### [v2.6.2](https://github.com/youzan/vant/compare/v2.6.1...v2.6.2)

`2020-04-18`

**Bug Fixes**

- Checkbox: get changed value in click event [#6066](https://github.com/youzan/vant/issues/6066)
- Picker: set<PERSON><PERSON>umn<PERSON><PERSON><PERSON> failed in cascade mode [#6080](https://github.com/youzan/vant/issues/6080)
- Slider: incorrect bar-height when vertical [#6065](https://github.com/youzan/vant/issues/6065)
- Swipe: incorrect width after resize if hidden [#6084](https://github.com/youzan/vant/issues/6084)

**Document**

- AddressEdit: add area-placeholder version tag [4d0281](https://github.com/youzan/vant/commit/4d0281018aaf810f7c8acdcb5c412578fd6579c7)
- changelog: 2.6.1 [307dc0](https://github.com/youzan/vant/commit/307dc01d50c4495769153ec0a4d33b977b7e6451)
- Image: rename Image to VanImage [#6049](https://github.com/youzan/vant/issues/6049)

**Feature**

- Empty: support offline scenario [#6055](https://github.com/youzan/vant/issues/6055)
- eslint-config: extends typescript-eslint recommended [#6076](https://github.com/youzan/vant/issues/6076)
- NoticeBar: add replay event [#6079](https://github.com/youzan/vant/issues/6079)
- Overlay: add lock-scroll prop [#6082](https://github.com/youzan/vant/issues/6082)
- Uploader: add lazy-load prop [#6083](https://github.com/youzan/vant/issues/6083)
