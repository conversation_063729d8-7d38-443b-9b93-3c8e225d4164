<!--
 * @description: 抽离开源版本
 * @Author: chuyin<PERSON>
 * @Date: 2021-08-20 17:29:06
 * @LastEditors: latiao
 * @LastEditTime: 2022-01-10 15:41:27
 2025.5.28有改动的页面
-->
<template>
  <m-layout :routes="routes" :breadcrumb="breadcrumb" :user-info="userInfo">
    <el-breadcrumb slot="breadcrumb" separator="/" class="m__breadcrumb">
      <el-breadcrumb-item v-for="(item, i) of breadcrumb" :class="[
    'm__breadcrumb--item',
    { 'm__breadcrumb--last': i !== breadcrumb.length - 1 }
  ]" :to="i !== breadcrumb.length - 1 && i !== 0
    ? {
      path:
        item.path + ('/goods' === item.path ? '?loadCache=true' : '/integral/integral' === item.path ? '?loadCache=true' : '')
    }
    : null
    " :key="item.path">{{ item.title }}
      </el-breadcrumb-item>
    </el-breadcrumb>
    <div slot="aside" class="side-nav-main">
      <div class="side-nav">
        <MMenu :routes="routes" :userInfo="userInfo">
          <el-dropdown placement="bottom-start" slot="dropmenu" @command="commandHandle">
            <div class="lineFlex">
              <img @click.stop="goChange" :src="shopInfo.logoUrl" class="shop--logo" />
              <div class="shop--name">
                <span class="el-dropdown-link">
                  <span>{{ shopInfo.shopName }}</span>
                  <el-badge :value="messageCount" class="item">
                    <i class="el-icon-bell" style="font-size: 15px;"></i>
                  </el-badge>
                  <button @click="playAudio" style="display: none;"></button>
                  <!-- <i class="el-icon-caret-bottom"></i> -->
                </span>
                <el-dropdown-menu slot="dropdown">
                  <!-- <el-dropdown-item command="console">
                    <div class="dorp-cell">
                      <span>控制台</span>
                    </div>
                  </el-dropdown-item> -->
                  <el-dropdown-item command="index">
                    <div class="dorp-cell">
                      <span>官网首页</span>
                    </div>
                  </el-dropdown-item>
                  <el-dropdown-item command="setting">
                    <div class="dorp-cell">
                      <span>商家中心</span>
                      <span class="c6">{{ userInfo.name }}</span>
                      <i class="el-icon-arrow-right"></i>
                    </div>
                  </el-dropdown-item>
                  <el-dropdown-item command="messageList">
                    <div class="dorp-cell">
                      <span>消息提醒</span>
                      <i class="el-icon-sunrise"></i>
                    </div>
                  </el-dropdown-item>
                  <el-dropdown-item command="logout">
                    <div class="dorp-cell noborder">
                      <span>退出登录</span>
                      <i class="el-icon-switch-button"></i>
                    </div>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </div>
            </div>
          </el-dropdown>
        </MMenu>
        <!--        <a class="business" href="https://www.bgniao.cn/" target="_blank">-->
        <!--          <p>商业版</p>-->
        <!--          <div class="img"><img src="@/assets/images/sj.png" alt=""></div>-->
        <!--        </a>-->
      </div>
    </div>
    <template slot="view">
      <router-view slot="view" :key="$route.path + $route.query.t"></router-view>
    </template>
  </m-layout>
</template>

<script lang="ts">
import { Vue, Component } from "vue-property-decorator";
import MLayout from "@/components/packages/layout";
import MMenu from "@/components/packages/layout/MMenu.vue";
import { logout, modifySignStatus } from "@/libs";
import { getSystemConfig } from "@/api/sign";
import {
  getOrderManageSetting,
  getOrders
} from "@/api/order/index";
import { pageList} from "@/api/message/message";
import { connectMessageSSE } from "@/api/sse/sse";
import { userStore } from "@/store/modules/user";
@Component({ components: { MLayout, MMenu } })
export default class MMain extends Vue {
  // mock
  logo = "";
  orderNotifyInterval = 0;
  messageCount = 0;
  orderNotify = false;

  // SSE相关属性
  sseReader: ReadableStreamDefaultReader<Uint8Array> | null = null;
  sseAbortController: AbortController | null = null;
  clientId = (userStore.userInfo.token) || '';
  reconnectAttempts = 0;
  maxReconnectAttempts = 30;//30次
  reconnectInterval = 6000;//6s
  get routes(): any[] {
    return (this.$router as any).options.routes;
  }

  get breadcrumb() {
    return this.$route.matched
      .map((route) => {
        return {
          title: route.meta.title,
          path: route.path
        };
      })
      .filter((item) => !!item.title);
  }

  get active() {
    return this.$route.fullPath;
  }

  /** 用户数据 */
  get userInfo() {
    const info = {
      name: "",
      avatar: "",
      logo: "",
      shopName: ""
    };
    if (this.$STORE.userStore.userInfo) {
      const userInfo = this.$STORE.userStore.userInfo;
      info.name = userInfo.nikeName;
      info.avatar = userInfo.avatarUrl;
      if (userInfo.shopInfoVo) {
        info.logo = userInfo.shopInfoVo.logoUrl;
        info.shopName = userInfo.shopInfoVo.shopName;
      }
    }
    return info;
  }

  /** 店铺数据 */
  get shopInfo() {
    let shopInfo = {
      businessHours: "",
      dueTime: "",
      isDue: 0,
      level: 0,
      logoUrl: "",
      packageName: "",
      shopName: "",
      shopPhone: "",
      status: 0,
      templateName: ""
    };
    if (this.$STORE.userStore.shopInfo) {
      shopInfo = this.$STORE.userStore.shopInfo;
    }
    return shopInfo;
  }
  // mounted(){
  //       // v-for="(item, i) of breadcrumb"
  //   console.log('***********',this.breadcrumb);

  // }


  created() {
    getOrderManageSetting()
      .then((res) => {
        if (res.data) {
          this.orderNotifyInterval = res.data.orderNotifyInterval
          this.orderNotify = res.data.orderNotify
          this.getOrdeorderNotify2();
          this.getOrdeorderNotify();
         /* setInterval(this.getOrdeorderNotify, this.orderNotifyInterval * 1000);*/
          // 建立SSE连接
          this.initSSEConnection();
        }
      })
      .catch((err) => {
        this.$message.warning(err);
      });
  }
  getOrdeorderNotify2() {
    var params = {};
    params.status = 0;
    pageList(params)
      .then((res) => {
        this.messageCount = res.data.total
      })
      .catch(() => {

      });
  }
  beforeDestroy() {
    // 组件销毁时关闭SSE连接
    this.closeSSEConnection();
  }
  // 初始化获取消息数量
  getOrdeorderNotify() {
    var params = {};
    params.status = 0;
    pageList(params)
      .then((res) => {
        this.messageCount = res.data.total
        if (this.orderNotify && this.messageCount > 0) {
          this.$el.querySelector('button').click();
        }
      })
      .catch(() => {

      });
  }

  // 初始化SSE连接
  async initSSEConnection() {
    try {
      // 创建AbortController用于取消请求
      this.sseAbortController = new AbortController();
      const sseConfig = connectMessageSSE(this.clientId,userStore);

      const response = await fetch(sseConfig.url, {
        method: 'GET',
        headers: sseConfig.headers,
        signal: this.sseAbortController.signal
      });

      if (!response.ok || !response.body) {
        throw new Error(`SSE连接失败: ${response.status} ${response.statusText}`);
      }

      this.sseReader = response.body.getReader();
      this.readSSEStream(this.sseReader, new TextDecoder('utf-8'));
    } catch (error) {
      console.error('SSE连接失败:', error);
      this.handleSSEError();
    }
  }

  // 读取SSE流数据
  async readSSEStream(reader: ReadableStreamDefaultReader<Uint8Array>, decoder: TextDecoder) {
    try {
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          console.log('SSE流结束');
          this.closeSSEConnection();
          break;
        }

        buffer += decoder.decode(value, { stream: true });
        const events = buffer.split(/\r?\n\r?\n/);
        buffer = events.pop() || '';

        // 处理完整的事件
        for (const event of events) {
          if (event.trim()) {
            this.parseSSEEvent(event.trim());
          }
        }
      }
    } catch (error) {
      console.error('SSE流读取失败:', error);
      this.handleSSEError();
    }
  }

  // 解析SSE事件
  parseSSEEvent(eventText: string) {
    const lines = eventText.split(/\r?\n/);
    let eventType = '';
    let data = '';

    for (const line of lines) {
      if (line.startsWith('event:')) {
        eventType = line.replace(/^event:\s*/, '').trim();
      } else if (line.startsWith('data:')) {
        const lineData = line.replace(/^data:\s*/, '').trim();
        data += (data ? '\n' : '') + lineData;
      }
    }

    if (data) {
      this.handleSSEMessage(data, eventType);
    }
  }

  // 处理SSE消息
  handleSSEMessage(data: string, eventType?: string) {
    console.log('开始处理SSE消息:', { data, eventType });

   
    try {
    
      // 根据事件类型或消息内容类型处理
     if(eventType == 'callback' ){
        console.log("sseCallback",data);
        this.reconnectAttempts = 0;
      }else if(eventType == 'messageCount' || eventType == 'newMessage' ){
  
        const messageData = JSON.parse(data);
        const messageType = eventType || messageData.type;

        if (messageType === 'messageCount' || messageData.type === 'messageCount') {
          this.messageCount = messageData.total || 0;
         } else if (messageType === 'newMessage' || messageData.type === 'newMessage') {
          // 有新消息时，更新数量并播放提示音
          this.messageCount = messageData.total || 0;
          if (this.orderNotify && this.messageCount > 0) {
            this.playAudio();
          }
       }
      }
      
    } catch (error) {
      console.log('消息不是JSON格式，不处理:', data);
    }
  }


  // 处理SSE错误
  handleSSEError() {
    this.closeSSEConnection();

    // 重连逻辑
    if ( this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`SSE连接断开，${this.reconnectInterval}ms后进行第${this.reconnectAttempts}次重连`);
      setTimeout(() => {
        this.initSSEConnection();
      }, this.reconnectInterval);
    } else {
      console.error('SSE重连次数已达上限，停止重连');
    }
  }

  // 关闭SSE连接
  closeSSEConnection() {
    if (this.sseAbortController) {
      this.sseAbortController.abort();
      this.sseAbortController = null;
    }
    if (this.sseReader) {
      this.sseReader.cancel();
      this.sseReader = null;
    }
    console.log('SSE连接已关闭');
  }
  playAudio() {
    this.$STORE.globalStore.handlePlay();
    console.log("playAudio");
    
  }
  async commandHandle(command: string) {
    // 退出登录
    if (command === "logout") {
      modifySignStatus("", null);
      logout();
      try {
        const response = await getSystemConfig();
        const { code, data } = response;
        if (code === 200 && data.systemConfig && data.systemConfig.consoleLog) {
          open(data.systemConfig.consoleLog, "_top");
        }
        this.$router.go("/sign");
      } catch (e) {
        this.$message({
          type: "warning",
          message: e
        });
      }
    }
    // 账号信息
    if (command === "setting") {
      await this.$router.push("/business");
    }

    // 控制台
    if (command === "console") {
      await this.$router.push("/console");
    }

    // 官网
    if (command === "index") {
      const url = "http://www.nnlyd.com/";
      open(`${url}`);
    }

    // 订购
    if (command === "order") {
      await this.$router.push("/meal");
    }
    // 升级/续费
    if (command === "update") {
      await this.$router.push("/meal/update");
    }

    //订单列表
    if (command === "messageList") {
      this.$STORE.globalStore.handlePlay();
      console.log("messageList");
      //await this.$router.push({ path: '/order/delivery', query: { orderStatus: '1', t: Date.now() } }).catch(() => { })
      await this.$router.push("/message");
    }
  }

  async goConsole() {
    try {
      const response = await getSystemConfig();
      const { code, data } = response;
      if (code === 200 && data.systemConfig && data.systemConfig.consoleUrl) {
        open(`${data.systemConfig.consoleUrl}`, "_top");
      }
    } catch (e) {
      this.$message({
        type: "warning",
        message: e
      });
    }
  }

  goChange() {
    this.$router.push({
      path: "/setting",
      query: {
        tabName: "Store"
      }
    });
  }
}
</script>

<style lang="scss">
.lineFlex {
  display: flex;
  align-items: center;
  height: 60px;
  cursor: pointer;
}

.m__breadcrumb {
  &--item {
    .is-link {
      font-weight: 400 !important;
      color: #606266 !important;
      cursor: pointer;
    }

    &:last-child .el-breadcrumb__inner {
      font-weight: 700 !important;
      text-decoration: none;
      transition: color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
      color: #303133;
    }
  }
}

.business {
  position: fixed;
  border-radius: 50px;
  width: 106px;
  height: 38px;
  background-color: #fff;
  bottom: 11px;
  right: 13px;

  p {
    position: absolute;
    font-family: "SimHei";
    font-size: 15px;
    font-weight: 600;
    top: 11px;
    left: 21px;
    color: #59a9f5;
  }

  .img {
    position: absolute;
    right: 20px;
    top: 13px;
    width: 14px;
    height: 14px;
  }
}

.el-dropdown-link {
  display: flex;
  height: 50px;
  align-items: center;
}
</style>
