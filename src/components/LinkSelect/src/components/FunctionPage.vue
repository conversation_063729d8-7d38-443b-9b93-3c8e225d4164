<!--
 * @description: 抽离开源版本
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2021-08-20 17:29:06
 * @LastEditors: latiao
 * @LastEditTime: 2022-01-14 10:08:27
 2025-7-17
-->
<template>
  <!-- 功能页面 -->
  <div>
    <el-table :data="tableData" height="369">
      <el-table-column label="页面名称" prop="name"></el-table-column>
      <el-table-column label="操作" width="100px">
        <template slot-scope="scope">
          <el-radio v-model="selectId" @change="selectHandle" :label="scope.row.id">
            <span></span>
          </el-radio>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, PropSync, Watch } from "vue-property-decorator";
import LinkSelectItem, { typeNameMap } from "./LinkSelectItem";
import { getPageList } from "@/api/group/group";
import { getTemplate } from "@/api/decoration/decoration";

/** 功能页面link数据的id和type值不允许更改 */
@Component
export default class LinkSelect extends Vue {
  @PropSync("link", {
    type: Object,
    default: () => {
      return {
        id: null,
        type: null,
        name: "",
        url: "",
        append: ""
      };
    }
  })
  linkSelectItem!: LinkSelectItem;

  @Prop({
    type: Boolean,
    default: false
  })
  visible!: boolean;

  @Prop({ default: false }) // 底部导航去客服
  noProTab!: boolean;

  @Prop({ default: true }) // 底部导航限制可选范围
  limitProTab!: boolean;

  name = "FunctionPage";

  selectId: string | number = "";

  searchName = "";

  templateId = "";

  searchData: LinkSelectItem[] = []; // 暂存数据

  get tableData() {
    let tableData = [
      {
        id: 0,
        type: 0,
        name: "店铺主页",
        url: "/pages/index/index",
        append: "home"
      },
      {
        id: 3,
        type: 0,
        name: "个人中心",
        url: "/pages/index/index",
        append: "me"
      },
      {
        id: 4,
        type: 0,
        name: "购物车",
        url: "/pages/index/index",
        append: "shopCar"
      },
      // {
      //   id: 26,
      //   type: 0,
      //   name: "运营中心",
      //   url: "/pages/index/index",
      //   append: "operationCenter"
      // },
      {
        id: 5,
        type: 0,
        name: "资源入驻",
        url: "/subcontract/pages/resourceApply/resourceApply",
        append: ""
      },
      {
        id: 7,
        type: 0,
        name: "地址管理",
        url: "/subcontract/pages/address/address",
        append: ""
      },
      {
        id: 8,
        type: 0,
        name: "客服",
        url: "",
        append: ""
      },
      {
        id: 9,
        type: 0,
        name: "设置",
        url: "/subcontract/pages/mySetting/mySetting",
        append: ""
      },
      {
        id: 10,
        type: 0,
        name: "历史购买",
        url: "/subcontract/pages/order/order",
        append: ""
      },
      {
        id: 11,
        type: 0,
        name: "最近购买",
        url: "/subcontract/pages/recentOrder/recentOrder",
        append: ""
      },
      {
        id: 13,
        type: 0,
        name: "通惠证",
        url: "/modules/pages/myCoupon/myCoupon",
        append: "modules"
      },
      {
        id: 14,
        type: 0,
        name: "我的佣金",
        url: "/modules/pages/myCommission/myCommission",
        append: "modules"
      },
      {
        id: 15,
        type: 0,
        name: "积分商城",
        url: "/modules/pages/pointsMall/pointsMall",
        append: "modules"
      },
      {
        id: 16,
        type: 0,
        name: "我的团队",
        url: "/modules/pages/myTeam/myTeam",
        append: "modules"
      },
      {
        id: 17,
        type: 0,
        name: "商家入驻",
        url: "/modules/pages/merchantSettlement/merchantSettlement",
        append: "modules"
      },
      {
        id: 18,
        type: 0,
        name: "商家信息",
        url: "/merchant/pages/Information/Information",
        append: "merchant"
      },
      {
        id: 19,
        type: 0,
        name: "商家分类",
        url: "/pages/index/index",
        append: "mall"
      },
      {
        id: 20,
        type: 0,
        name: "权益包商品",
        url: "/modules/pages/package/packageGoodsList",
        append: "modules"
      },
      {
        id: 21,
        type: 0,
        name: "代注册",
        url: "/turntable/pages/loginApp/loginAppNew",
        append: "turntable"
      },
      {
        id: 22,
        type: 0,
        name: "预约单",
        url: "/turntable/pages/reservation/reservationForm",
        append: "turntable"
      },
      {
        id: 23,
        type: 0,
        name: "会员升级",
        url: "/turntable/pages/membershipUpgrade/membershipUpgrade",
        append: "turntable"
      },
      {
        id: 24,
        type: 0,
        name: "待发货列表",
        url: "/turntable/pages/remainderNum/remainderNum",
        append: "turntable"
      },
      {
        id: 25,
        type: 0,
        name: "代发货",
        url: "/turntable/pages/shipment/shipmentDetails",
        append: "turntable"
      },
      {
        id: 27,
        type: 0,
        name: "区域申请列表",
        url: "/turntable/pages/regionalMember/regionalApplicationList",
        append: "turntable"
      },
      {
        id: 28,
        type: 0,
        name: "分享按钮",
        url: "/pages/index/index",
        append: "me"
      },
      {
        id: 29,
        type: 0,
        name: "个人信息",
        url: "/pages/personalInformation/personalInformation",
        append: "subcontract"
      },
      {
        id: 30,
        type: 0,
        name: "订单列表",
        url: "/subcontract/pages/order/order",
        append: "subcontract"
      },
      {
        id: 31,
        type: 0,
        name: "优惠券",
        url: "/modules/pages/myCard/myCard",
        append: "modules"
      },
      {
        id: 32,
        type: 0,
        name: "抽奖",
        url: "/turntable/pages/myHome/myHome",
        append: "turntable"
      },
      {
        id: 33,
        type: 0,
        name: "金豆",
        url: "/turntable/pages/goldenBean/goldenBean",
        append: "turntable"
      },
      {
        id: 34,
        type: 0,
        name: "获奖记录",
        url: "/turntable/pages/myHome/awardRecord",
        append: "turntable"
      },
      {
        id: 35,
        type: 0,
        name: "收藏",
        url: "/subcontract/pages/collect/collect",
        append: "subcontract"
      },
      {
        id: 36,
        type: 0,
        name: "足迹",
        url: "/subcontract/pages/footer/footer",
        append: "subcontract"
      },
      // {
      //   id: 19,
      //   type: 0,
      //   name: "商家分类",
      //   url: "/merchant/pages/merchantClassification/merchantClassification",
      //   append: "merchant"
      // }
      // merchant/pages/merchantClassification/merchantClassification
    ];

    if (
      !Reflect.has(this.$STORE.userStore, "isPointOpen") ||
      this.$STORE.userStore.shopInfo.level === 1
    ) {
      return tableData.filter((item) => {
        // console.log('131', item)
        return item.id !== 12;
      });
    } else {
      return tableData;
    }
  }

  mounted() {
    this.getTemplateList();
    const level = this.$STORE.userStore.shopInfo.level;
    if (level === 1) {
      this.tableData.forEach((item, index) => {
        if (item.id === 12) {
          this.tableData.splice(index, 1);
        }
      });
    }
    // 初始相同设为选中状态
    if (this.sameJudge()) {
      // console.log('150', this.linkSelectItem)
      this.selectId = this.linkSelectItem.id;
    }
    this.searchData = this.tableData;
    if (this.noProTab) {
      const tableData = this.tableData;
      // console.log('155', tableData)
      for (let i = 0; i < tableData.length; i++) {
        if (tableData[i].id == 8) {
          tableData.splice(i, 1);
          break;
        }
      }
    }
    let tableData = this.tableData;

    if (!this.limitProTab) {
      // console.log('165', tableData)
      for (let i = 0; i < tableData.length; i++) {
        if (
          tableData[i].id == 5 ||
          tableData[i].id == 7 ||
          tableData[i].id == 8 ||
          tableData[i].id == 9 ||
          tableData[i].id == 11 ||
          tableData[i].id == 12 ||
          tableData[i].id == 14
        ) {
          tableData.splice(i, 1);
          i -= 1;
        }
        /*if (tableData[i].id == 7) {
          tableData.splice(i, 1);
        }
        if (tableData[i].id == 8) {
          tableData.splice(i, 1);
        }
        if (tableData[i].id == 9) {
          tableData.splice(i, 1);
        }
        if (tableData[i].id == 11) {
          tableData.splice(i, 1);
        }
        if (tableData[i].id == 12) {
          tableData.splice(i, 1);
        }
        if (tableData[i].id == 14) {
          tableData.splice(i, 1);
        }*/
      }
    }
  }

  // 监听父弹窗显隐
  @Watch("visible")
  handleVisibleChange(v: boolean) {
    if (v) {
      // 初始相同设为选中状态
      if (this.sameJudge()) {
        // console.log('197', this.linkSelectItem)
        this.selectId = this.linkSelectItem.id;
      }
    }
  }

  selectHandle() {
    const selectId = this.selectId;
    const currentItem: LinkSelectItem =
      this.tableData.find((item) => item.id === selectId) ||
      ({} as LinkSelectItem);
    Object.assign(this.linkSelectItem, currentItem);
  }

  sameJudge(): boolean {
    return (
      typeNameMap[this.linkSelectItem.type] &&
      typeNameMap[this.linkSelectItem.type].name === this.name
    );
  }

  /**
   * 获取模板列表
   * isDevTemplate 默认模板 1是 0否
   * 模板是否使用中 0 否, 1 是
   * 自定义没有
   */
  async getTemplateList() {
    const getPageListData = {
      onlineStatus: 1,
      isAll: 0
    };
    const res = await getTemplate(getPageListData);
    try {
      if (res.code === 200) {
        /**  获取非默认模板（即为正在使用中的模板） */
        if (res.data.length > 0) {
          /** 如果有，即为使用中模板的curd */
          // console.log('234', res.data[0])
          this.templateId = res.data[0].id;
          this.getPageList();
        }
      } else {
        this.$message.warning(`获取数据失败`);
      }
    } catch (e) {
      console.log(e);
    }
  }

  /** 获取自定义页面数据 */
  async getPageList() {
    const data = {
      templateId: this.templateId,
      size: 100
    };
    const res = await getPageList(data);
    try {
      if (res.code === 200) {
        // console.log('254', this.tableData[0])
        this.tableData[0].id = res.data.list.filter(
          (item: { isDef: string }) => item.isDef !== "0"
        )[0].id; // 初始化获取 设为首页的页面id
      } else {
        this.$message.warning(`获取数据失败`);
      }
    } catch (e) {
      console.log(e);
    }
  }
}
</script>
<style scoped>
.search-wrap {
  display: flex;
  justify-content: space-between;
  justify-items: center;
}

.search-wrap-input {
  width: 180px;
}
</style>
