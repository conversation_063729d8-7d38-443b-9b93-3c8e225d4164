@import "./variable";

.admin {
  width: 100vw;
  height: 100vh;
  background: #eef1f6;
  overflow: auto;

  &__header {
    @include flex(space-between);

    background: #fff;
    margin-bottom: 10px;
    padding: 15px 0;

    &--logo {
      width: 128px;
      height: auto;
      display: block;
    }

    &--user {
      @include flex(flex-end);

      // .user--name {
      // }

      .user--avatar {
        display: block;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin: 0 15px;
      }

      .el-icon-bell {
        font-size: 24px;
      }
    }
  }

  &__center {
    width: 1200px;
    min-width: 1200px !important;
    margin: 0 auto;

    &--flex {
      @include flex(space-between);
    }
  }

  &__content {
    padding-bottom: 10px;
    @include flex(space-between, flex-start);

    align-items: stretch;
    min-height: calc(100% - 70px);
  }

  &__main {
    @mixin c-s {
      background: #fff;
      padding: 20px 15px;
    }
    padding: 0;

    &--breadcrumb {
      @include c-s;
      margin-bottom: 10px;
    }
    &--content {
      @include c-s;
    }
  }

  &__aside {
    margin-right: 10px;
    background: #fff;
    // overflow-x: hidden !important;
    flex: 0 0 180px;

    &--shop {
      @include flex;

      flex-direction: column;
      width: 100%;
      height: 200px;

      .shop--logo {
        width: 80px;
        height: 80px;
      }
    }

    // cover
    .el-menu {
      border: none !important;
    }

    .el-menu-item.is-active {
      background: #f0faff;

      &::after {
        content: "";
        display: block;
        width: 2px;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        background: #2d8cf0;
      }
    }
  }
}
