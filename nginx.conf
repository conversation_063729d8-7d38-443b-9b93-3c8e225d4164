user root;
worker_processes  1;

events {
    worker_connections  8096;
}

http {

	include       mime.types;
    default_type  application/octet-stream;


    server {
        listen  8047;
        charset utf-8;
        server_name  *************;

		location / {
            gzip_static on; 
            root   /usr/share/nginx/html/app-mall;
			index  index.html index.htm;
			try_files $uri $uri/ /index.html;
			
		}
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }
}
